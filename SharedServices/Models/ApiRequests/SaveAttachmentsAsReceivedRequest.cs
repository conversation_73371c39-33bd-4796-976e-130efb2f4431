using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class SaveAttachmentsAsReceivedRequest
    {
        [JsonProperty("siteUrl")]
        public string? SiteUrl { get; set; }

        [JsonProperty("attachments")]
        public List<AttachmentRecord>? Attachments { get; set; }
    }

    public class AttachmentRecord
    {
        [JsonProperty("emailId")]
        public string? EmailId { get; set; }

        [JsonProperty("recordName")]
        public string? RecordName { get; set; }

        [JsonProperty("recordTitle")]
        public string? RecordTitle { get; set; }

        [JsonProperty("revision")]
        public string? Revision { get; set; }
    }
}
