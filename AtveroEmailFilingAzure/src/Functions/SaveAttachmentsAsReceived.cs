using System.Net;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class SaveAttachmentsAsReceived
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<SaveAttachmentsAsReceived> _logger;

        public SaveAttachmentsAsReceived(
            ITokenValidationService tokenValidationService,
            ILogger<SaveAttachmentsAsReceived> logger
        )
        {
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [Function("SaveAttachmentsAsReceived")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to save attachments as received at {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // Validate the token
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);
                if (token == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Invalid or missing authorization token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                // Read and parse the request body
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                SaveAttachmentsAsReceivedRequest? request;
                try
                {
                    request = JsonConvert.DeserializeObject<SaveAttachmentsAsReceivedRequest>(
                        requestBody
                    );
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to decode JSON request");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                // Validate request data
                if (
                    request == null
                    || string.IsNullOrEmpty(request.SiteUrl)
                    || request.Attachments == null
                    || !request.Attachments.Any()
                )
                {
                    _logger.LogWarning("Invalid request data: missing siteUrl or attachments.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data. SiteUrl and Attachments are required.",
                        _logger
                    );
                }

                // Validate SharePoint site URL format
                if (!Uri.TryCreate(request.SiteUrl, UriKind.Absolute, out Uri? siteUri))
                {
                    _logger.LogWarning(
                        "Invalid SharePoint site URL format: {SiteUrl}",
                        request.SiteUrl
                    );
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid SharePoint site URL format.",
                        _logger
                    );
                }

                // Validate attachment records
                foreach (var attachment in request.Attachments)
                {
                    if (
                        string.IsNullOrEmpty(attachment.EmailId)
                        || string.IsNullOrEmpty(attachment.RecordName)
                        || string.IsNullOrEmpty(attachment.RecordTitle)
                    )
                    {
                        _logger.LogWarning("Invalid attachment record: missing required fields.");
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Invalid attachment record. EmailId, RecordName, and RecordTitle are required.",
                            _logger
                        );
                    }
                }

                // TODO: Implement the actual attachment processing logic here
                // This would typically involve:
                // 1. Connecting to SharePoint using the site URL
                // 2. Processing each attachment record
                // 3. Saving/updating records in SharePoint lists
                // 4. Handling any file operations

                _logger.LogInformation(
                    "Processing {AttachmentCount} attachments for site {SiteUrl}",
                    request.Attachments.Count,
                    request.SiteUrl
                );

                // For now, return a success response indicating the request was processed
                var response = new
                {
                    SiteUrl = request.SiteUrl,
                    ProcessedAttachments = request.Attachments.Count,
                    ProcessedAt = DateTime.UtcNow,
                    Message = "Attachments processed successfully",
                };

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    response,
                    "Attachments saved as received successfully"
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
