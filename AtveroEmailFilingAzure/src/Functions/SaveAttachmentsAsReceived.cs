using System.Net;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using CMapPim.Model;
using CMapPim.Services;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class SaveAttachmentsAsReceived
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;
        private readonly ICMapPimService _cmapPimService;
        private readonly ILogger<SaveAttachmentsAsReceived> _logger;

        public SaveAttachmentsAsReceived(
            ITokenValidationService tokenValidationService,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService,
            ICMapPimService cmapPimService,
            ILogger<SaveAttachmentsAsReceived> logger
        )
        {
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _cmapPimService =
                cmapPimService ?? throw new ArgumentNullException(nameof(cmapPimService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [Function("SaveAttachmentsAsReceived")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to save attachments as received at {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // Validate the token
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);
                if (token == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Invalid or missing authorization token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                // Read and parse the request body
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                SaveAttachmentsAsReceivedRequest? request;
                try
                {
                    request = JsonConvert.DeserializeObject<SaveAttachmentsAsReceivedRequest>(
                        requestBody
                    );
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to decode JSON request");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                // Validate request data
                if (
                    request == null
                    || string.IsNullOrEmpty(request.SiteUrl)
                    || request.Attachments == null
                    || !request.Attachments.Any()
                )
                {
                    _logger.LogWarning("Invalid request data: missing siteUrl or attachments.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data. SiteUrl and Attachments are required.",
                        _logger
                    );
                }

                // Validate SharePoint site URL format
                if (!Uri.TryCreate(request.SiteUrl, UriKind.Absolute, out Uri? siteUri))
                {
                    _logger.LogWarning(
                        "Invalid SharePoint site URL format: {SiteUrl}",
                        request.SiteUrl
                    );
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid SharePoint site URL format.",
                        _logger
                    );
                }

                // Validate attachment records
                foreach (var attachment in request.Attachments)
                {
                    if (
                        string.IsNullOrEmpty(attachment.EmailId)
                        || string.IsNullOrEmpty(attachment.RecordName)
                        || string.IsNullOrEmpty(attachment.RecordTitle)
                    )
                    {
                        _logger.LogWarning("Invalid attachment record: missing required fields.");
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Invalid attachment record. EmailId, RecordName, and RecordTitle are required.",
                            _logger
                        );
                    }
                }

                // Create GraphServiceClient for the user
                var graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );

                if (graphClient == null)
                {
                    _logger.LogError("Failed to create GraphServiceClient");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create Graph client.",
                        _logger
                    );
                }

                // Get the site information from the SharePoint URL
                Site? site = await _graphApiClient.GetSiteAsync(request.SiteUrl, graphClient);
                if (site == null)
                {
                    _logger.LogError(
                        "Failed to get site information for URL: {SiteUrl}",
                        request.SiteUrl
                    );
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to access SharePoint site. Please verify the site URL and permissions.",
                        _logger
                    );
                }

                // Create the CMapPim Context
                Context pimContext = new Context
                {
                    SiteId = site.Id,
                    Logger = _logger,
                    GraphApiClient = _graphApiClient,
                    GraphClient = graphClient,
                };

                _logger.LogInformation(
                    "Processing {AttachmentCount} attachments for site {SiteUrl} (Site ID: {SiteId})",
                    request.Attachments.Count,
                    request.SiteUrl,
                    site.Id
                );

                // Process each attachment record
                var processedRecords = new List<object>();
                foreach (var attachment in request.Attachments)
                {
                    try
                    {
                        // TODO: Implement the actual attachment processing logic here
                        // This would typically involve:
                        // 1. Using the CMapPim service to find/create records
                        // 2. Processing email attachments
                        // 3. Saving/updating records in SharePoint lists
                        // 4. Handling any file operations

                        _logger.LogInformation(
                            "Processing attachment: EmailId={EmailId}, RecordName={RecordName}, RecordTitle={RecordTitle}, Revision={Revision}",
                            attachment.EmailId,
                            attachment.RecordName,
                            attachment.RecordTitle,
                            attachment.Revision
                        );

                        // For now, just add to processed records list
                        processedRecords.Add(
                            new
                            {
                                EmailId = attachment.EmailId,
                                RecordName = attachment.RecordName,
                                RecordTitle = attachment.RecordTitle,
                                Revision = attachment.Revision,
                                Status = "Processed",
                                ProcessedAt = DateTime.UtcNow,
                            }
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error processing attachment: {EmailId}",
                            attachment.EmailId
                        );
                        processedRecords.Add(
                            new
                            {
                                EmailId = attachment.EmailId,
                                RecordName = attachment.RecordName,
                                RecordTitle = attachment.RecordTitle,
                                Revision = attachment.Revision,
                                Status = "Failed",
                                Error = ex.Message,
                                ProcessedAt = DateTime.UtcNow,
                            }
                        );
                    }
                }

                var response = new
                {
                    SiteUrl = request.SiteUrl,
                    SiteId = site.Id,
                    ProcessedAttachments = request.Attachments.Count,
                    ProcessedAt = DateTime.UtcNow,
                    Message = "Attachments processed successfully",
                    Records = processedRecords,
                };

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    response,
                    "Attachments saved as received successfully"
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
